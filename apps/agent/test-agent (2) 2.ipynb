{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import logging\n", "logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from agents.claude_e2b import (\n", "    create_sandbox,\n", "    stream_claude_in_sandbox,\n", "\n", ")\n", "\n", "from db import db_manager\n", "\n", "await db_manager.connect()\n", "\n", "sandbox = await create_sandbox(timeout=1000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Stream Claude outputs in real-time\n", "async for output in stream_claude_in_sandbox(\n", "    sandbox,\n", "    \"hey please fix this https://github.com/backspace-org/backspace-mono/issues/88\",\n", "    claude_options={\"max-turns\": \"100\"},\n", "    timeout=0\n", "):\n", "    print(f\"[{output.timestamp:.2f}] {output.type}: {str(output.content)[:100]}\")\n", "    \n", "    # Special handling for different output types\n", "\n", "    if output.type == \"claude_message\":\n", "    print(f\"  🤖 <PERSON> said: {output.content}\")\n", "    elif output.type == \"tool_call\":\n", "        print(f\"  🔧 Tool: {output.content.get('name')}\")\n", "        if output.content.get('input'):\n", "            print(f\"      Input: {output.content.get('input')}\")\n", "    elif output.type == \"tool_result\":\n", "        is_error = output.content.get('is_error', False)\n", "        result_content = output.content.get('content', '')\n", "        print(f\"  {'❌' if is_error else '✅'} Tool Result: {result_content[:200]}...\")\n", "    elif output.type == \"result\":\n", "        print(f\"  🏁 Final result - Success: {not output.content.get('is_error')}\")\n", "\n", "print(\"\\n=== STREAMING COMPLETE ===\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}