import logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

from agents.claude_e2b import (
    create_sandbox,
    stream_claude_in_sandbox,

)

from db import db_manager

await db_manager.connect()

sandbox = await create_sandbox(timeout=1000)



# Stream Claude outputs in real-time
async for output in stream_claude_in_sandbox(
    sandbox,
    "hey please fix this https://github.com/backspace-org/backspace-mono/issues/88",
    claude_options={"max-turns": "100"},
    timeout=0
):
    print(f"[{output.timestamp:.2f}] {output.type}: {str(output.content)[:100]}")
    
    # Special handling for different output types
    if output.type == "claude_message":
        print(f"  🤖 <PERSON> said: {output.content}")
    elif output.type == "tool_call":
        print(f"  🔧 Tool: {output.content.get('name')}")
        if output.content.get('input'):
            print(f"      Input: {output.content.get('input')}")
    elif output.type == "tool_result":
        is_error = output.content.get('is_error', False)
        result_content = output.content.get('content', '')
        print(f"  {'❌' if is_error else '✅'} Tool Result: {result_content[:200]}...")
    elif output.type == "result":
        print(f"  🏁 Final result - Success: {not output.content.get('is_error')}")

print("\n=== STREAMING COMPLETE ===")