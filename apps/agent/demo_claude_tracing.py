#!/usr/bin/env python3
"""
Demo script showing Claude Code with LangSmith tracing integration.

This script demonstrates how to use the enhanced Claude Code integration
that captures each stream output and sends it to LangSmith for visualization.

Usage:
    python demo_claude_tracing.py

Environment Variables Required:
    LANGCHAIN_API_KEY: Your LangSmith API key
    LANGCHAIN_TRACING_V2: Set to "true" to enable tracing
    LANGCHAIN_PROJECT: Your LangSmith project name (optional)
"""

import asyncio
import os
import logging
from typing import Optional

from e2b_code_interpreter import AsyncSandbox
from src.agents.claude_e2b.claude import run_claude_in_sandbox, stream_claude_in_sandbox, ClaudeOutput

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_environment():
    """Check if required environment variables are set."""
    required_vars = {
        "LANGCHAIN_API_KEY": "LangSmith API key",
        "E2B_API_KEY": "E2B API key for sandbox"
    }
    
    missing_vars = []
    for var, description in required_vars.items():
        if not os.getenv(var):
            missing_vars.append(f"{var} ({description})")
    
    if missing_vars:
        logger.error("❌ Missing required environment variables:")
        for var in missing_vars:
            logger.error(f"   - {var}")
        return False
    
    # Set tracing environment variables if not already set
    if not os.getenv("LANGCHAIN_TRACING_V2"):
        os.environ["LANGCHAIN_TRACING_V2"] = "true"
        logger.info("🔍 Enabled LangChain tracing")
    
    if not os.getenv("LANGCHAIN_PROJECT"):
        os.environ["LANGCHAIN_PROJECT"] = "claude-code-tracing"
        logger.info("📊 Set LangSmith project to 'claude-code-tracing'")
    
    return True


async def demo_basic_tracing():
    """Demonstrate basic Claude Code execution with tracing."""
    logger.info("🚀 Starting basic tracing demo...")
    
    async with AsyncSandbox() as sandbox:
        logger.info(f"📦 Created sandbox: {sandbox.sandbox_id}")
        
        # Simple prompt to demonstrate tracing
        prompt = "List the files in the current directory and show me what's in the README if it exists"
        
        logger.info("🤖 Running Claude Code with tracing enabled...")
        session = await run_claude_in_sandbox(
            sandbox=sandbox,
            prompt=prompt,
            enable_tracing=True,
            timeout=60
        )
        
        logger.info("📊 Session Results:")
        logger.info(f"   ✅ Success: {session.success}")
        logger.info(f"   ⏱️  Duration: {session.elapsed_time:.2f}s")
        logger.info(f"   📝 Outputs: {len(session.outputs)}")
        if session.total_cost_usd:
            logger.info(f"   💰 Cost: ${session.total_cost_usd:.4f}")
        
        # Show trace URL if available
        if session.run_tree:
            logger.info(f"🔍 LangSmith Trace: Check your LangSmith dashboard")
        
        return session


async def demo_streaming_tracing():
    """Demonstrate streaming Claude Code execution with tracing."""
    logger.info("🚀 Starting streaming tracing demo...")
    
    async with AsyncSandbox() as sandbox:
        logger.info(f"📦 Created sandbox: {sandbox.sandbox_id}")
        
        # More complex prompt to show streaming
        prompt = """
        Create a simple Python script that:
        1. Prints 'Hello, World!'
        2. Lists the current directory contents
        3. Creates a small text file with some content
        4. Reads and displays the file content
        """
        
        logger.info("🤖 Streaming Claude Code with tracing enabled...")
        
        outputs = []
        async for output in stream_claude_in_sandbox(
            sandbox=sandbox,
            prompt=prompt,
            enable_tracing=True,
            timeout=120
        ):
            outputs.append(output)
            logger.info(f"📡 Streamed: {output.type} - {str(output.content)[:100]}...")
        
        logger.info("📊 Streaming Results:")
        logger.info(f"   📝 Total outputs streamed: {len(outputs)}")
        logger.info(f"🔍 Check your LangSmith dashboard for the trace visualization")
        
        return outputs


async def demo_custom_callback():
    """Demonstrate Claude Code with custom output callback and tracing."""
    logger.info("🚀 Starting custom callback demo...")
    
    # Custom callback to process outputs
    def custom_output_handler(output: ClaudeOutput):
        """Custom handler for Claude outputs."""
        if output.type == "claude_message":
            logger.info(f"🤖 Claude says: {output.content}")
        elif output.type == "tool_call":
            tool_name = output.content.get("name", "unknown")
            logger.info(f"🔧 Tool called: {tool_name}")
        elif output.type == "tool_result":
            is_error = output.content.get("is_error", False)
            status = "❌ Error" if is_error else "✅ Success"
            logger.info(f"🔧 Tool result: {status}")
    
    async with AsyncSandbox() as sandbox:
        logger.info(f"📦 Created sandbox: {sandbox.sandbox_id}")
        
        prompt = "Write a simple Python function to calculate fibonacci numbers and test it"
        
        logger.info("🤖 Running Claude Code with custom callback and tracing...")
        session = await run_claude_in_sandbox(
            sandbox=sandbox,
            prompt=prompt,
            on_output=custom_output_handler,
            enable_tracing=True,
            timeout=90
        )
        
        logger.info("📊 Custom Callback Results:")
        logger.info(f"   ✅ Success: {session.success}")
        logger.info(f"   📝 Outputs processed: {len(session.outputs)}")
        logger.info(f"🔍 Trace available in LangSmith dashboard")
        
        return session


async def main():
    """Main demo function."""
    logger.info("🎯 Claude Code LangSmith Tracing Demo")
    logger.info("=" * 50)
    
    # Check environment
    if not check_environment():
        logger.error("❌ Environment check failed. Please set required variables.")
        return
    
    try:
        # Run demos
        logger.info("\n" + "=" * 50)
        await demo_basic_tracing()
        
        logger.info("\n" + "=" * 50)
        await demo_streaming_tracing()
        
        logger.info("\n" + "=" * 50)
        await demo_custom_callback()
        
        logger.info("\n" + "=" * 50)
        logger.info("🎉 All demos completed successfully!")
        logger.info("🔍 Check your LangSmith dashboard to see the traces:")
        logger.info("   https://smith.langchain.com/")
        
    except Exception as e:
        logger.error(f"❌ Demo failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
