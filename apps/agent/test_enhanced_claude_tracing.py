#!/usr/bin/env python3
"""Test enhanced Claude tracing with <PERSON><PERSON><PERSON>."""

import asyncio
import os
import logging
from e2b_code_interpreter import AsyncSandbox
from agents.claude_e2b.claude import run_claude_in_sandbox, handle_claude_stream

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)

# Make sure <PERSON><PERSON><PERSON> is configured
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "claude-e2b-tracing-demo"
# Ensure LANGCHAIN_API_KEY is set in your environment


async def test_enhanced_tracing():
    """Test the enhanced Claude tracing."""
    
    # Create E2B sandbox
    sandbox = await AsyncSandbox.create()
    
    try:
        # Test prompt that will trigger various event types
        prompt = """
        Create a simple Python script that:
        1. Prints "Hello from <PERSON>!"
        2. Creates a file called test.txt with some content
        3. Lists the files in the current directory
        4. Reads the content of test.txt
        """
        
        logger.info("🚀 Starting Claude Code with enhanced tracing...")
        logger.info(f"📍 LangSmith Project: {os.getenv('LANGCHAIN_PROJECT')}")
        
        # Run Claude with tracing enabled
        session = await run_claude_in_sandbox(
            sandbox=sandbox,
            prompt=prompt,
            claude_options={
                "max-turns": "10",
                "model": "claude-3-5-sonnet-20241022"
            },
            enable_tracing=True
        )
        
        logger.info("\n📊 Session Summary:")
        logger.info(f"   ✅ Success: {session.success}")
        logger.info(f"   ⏱️  Duration: {session.elapsed_time:.2f}s")
        logger.info(f"   📝 Outputs collected: {len(session.outputs)}")
        if session.total_cost_usd:
            logger.info(f"   💰 Total cost: ${session.total_cost_usd:.4f}")
        
        # Display output types collected
        output_types = {}
        for output in session.outputs:
            output_types[output.type] = output_types.get(output.type, 0) + 1
        
        logger.info("\n📈 Output Types:")
        for output_type, count in output_types.items():
            logger.info(f"   - {output_type}: {count}")
        
        logger.info("\n✨ Check LangSmith for the detailed trace!")
        
    finally:
        await sandbox.close()


async def test_streaming_with_custom_handler():
    """Test streaming with custom output handler."""
    
    sandbox = await AsyncSandbox.create()
    
    try:
        # Custom handler to demonstrate real-time processing
        async def custom_output_handler(output):
            """Custom handler that could send to different systems."""
            # This is where you could:
            # - Send to a different logging system
            # - Update a UI in real-time
            # - Store in a database
            # - Send to a message queue
            logger.info(f"🔄 Custom Handler: {output.type} at {output.timestamp:.2f}")
        
        prompt = "Write a simple hello world Python script and run it."
        
        session = await run_claude_in_sandbox(
            sandbox=sandbox,
            prompt=prompt,
            on_output=custom_output_handler,
            enable_tracing=True
        )
        
        logger.info(f"\n✅ Streaming completed! Check LangSmith for trace.")
        
    finally:
        await sandbox.close()


if __name__ == "__main__":
    # Run the enhanced tracing test
    asyncio.run(test_enhanced_tracing())
    
    # Uncomment to test streaming with custom handler
    # asyncio.run(test_streaming_with_custom_handler()) 